package com.kanzhun.foundation.kernel.account;

import android.content.Context;

import com.kanzhun.foundation.utils.MeetingPlanModeChange;
import com.kanzhun.utils.L;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.common.util.SecurityUtils;
import com.kanzhun.foundation.bean.SMSLoginModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.router.LoginPageRouter;
import com.kanzhun.foundation.router.MainPageRouter;
import com.kanzhun.utils.SettingBuilder;

public class LoginUtil {

    public static void loginInit(SMSLoginModel response) {
        long apmUid = getApmUid(response);

        refreshAccountHelper(response, apmUid);

        insertUser(response, apmUid);
    }

    public static void insertUser(SMSLoginModel response, long apmUid) {
        User user = new User();
        user.setUserId(response.userId);
        user.setNickName(response.nickName);
        user.setNickNamePy(response.nickNamePy);
        user.setAvatar(response.avatar);
        user.setTinyAvatar(response.tinyAvatar);
        user.setLiveVideo(response.liveVideo);
        user.setGender(response.gender);
        user.setBirthday(response.birthday);
        user.setSchool(response.school);
        user.setSchoolLevel(response.schoolLevel);
        user.setSchoolArea(response.schoolArea);
        user.setPhase(response.phase);
        user.setApmUid(apmUid);
        user.setAccountType(response.accountType);
        if (response.matchStatus > 0) {
            user.setMatchStatus(response.matchStatus);
        }
        user.setCommunityLocked(response.communityLocked);
        user.setProfileLocked(response.profileLocked);
        if (response.mood != null) {
            user.setModeCode(response.mood.code);
            user.setModeName(response.mood.name);
            user.setModeIcon(response.mood.icon);
        }
        user.setShakeLocked(response.shakeLocked);
        if (response.childInfo != null) {
            user.setChildInfoGender(response.childInfo.gender);
            user.setChildInfoAddressCode(response.childInfo.addressCode);
            user.setChildInfoAddress(response.childInfo.address);
        }

        ServiceManager.getInstance().getDatabaseService().getUserDao().insert(user);
    }

    public static void refreshAccountHelper(SMSLoginModel response, long apmUid) {
        int parentPhase = 0;
        if (response.childInfo != null && response.childInfo.phase >= 10) {
            parentPhase = 1;
        }
        Account account = new Account(response.userId, response.sk, response.phase, apmUid, response.communityLocked, response.profileLocked, response.accountType, parentPhase, response.marryIntent, response.marryIntentCertStatus, response.inviteCodeType, response.customerInviteCodeH5Url, response.watermark,response.meetPlanUserStatus, response.meetPlanId);
        AccountHelper.getInstance().login(account);
        if(response.meetPlanUserStatus == 40){
            MeetingPlanModeChange.INSTANCE.setMeetingPlanMode(
                    MeetingPlanModeChange.Mode.MEETING_PLAN);
        }else {
            MeetingPlanModeChange.INSTANCE.setMeetingPlanMode(
                    MeetingPlanModeChange.Mode.NORMAL);
        }

    }

    public static long getApmUid(SMSLoginModel response) {
        long apmUid = 0;
        String rc4Decrypt = SecurityUtils.rc4Decrypt(response.apmUid, SettingBuilder.getInstance().getApmUidPassword());
        try {
            apmUid = Long.parseLong(rc4Decrypt);
        } catch (NumberFormatException e) {
            TLog.error("LoginViewModel", "apmUid 转换失败；rc4Decrypt = " + rc4Decrypt);
        }

        return apmUid;
    }

    public static void doLogin(Context context) {
        doLogin(context, false);
    }

    //去登录
    public static void doLogin(Context context, boolean clearTop) {
        //1、先判断是否登录
        if (AccountHelper.getInstance().isOnlyLogin()) {
            //2、判断是否要选择模式
            if (AccountHelper.getInstance().needSelectModel()) {
                LoginPageRouter.jumpToSelectModel(context, clearTop);
            } else if (AccountHelper.getInstance().isParent()) {
                if (AccountHelper.getInstance().needParentFirstEdit()) {
                    //父母模式首善
                    LoginPageRouter.jumpToParentFirstEdit(context, clearTop);
                }
            } else {
                //子女模式
                boolean isInvite = AccountHelper.getInstance().isInvite();//是否已经激活
                if (isInvite) {
                    if (editMarryPlan()) {
                        LoginPageRouter.jumpMarryPlan(context, true);
                    } else {
                        //首善
                        LoginPageRouter.jumpToChildFirstEdit(context, clearTop);
                    }
                } else {
                    //去激活
                    LoginPageRouter.jumpToInviteCode(context, clearTop);
                }
            }
        } else {
            LoginPageRouter.jumpToLoginActivity(context, clearTop);
        }
    }

    private static boolean editMarryPlan() {
        return AccountHelper.getInstance().editMarry();
    }


    //去首善或者主界面
    public static void goFirstEditOrMain(Context context) {
        if (AccountHelper.getInstance().isParent()) {
            if (AccountHelper.getInstance().needParentFirstEdit()) {
                //父母模式首善
                LoginPageRouter.jumpToParentFirstEdit(context, false);
            } else {
                //去主页面
                MainPageRouter.jumpToMainActivity(context);
            }
        } else {
            //子女模式
            boolean isInvite = AccountHelper.getInstance().isInvite();
            if (isInvite) {
                if (editMarryPlan()) {
                    LoginPageRouter.jumpMarryPlan(context, false);
                } else {
                    //首善
                    LoginPageRouter.jumpToChildFirstEdit(context, false);
                }
            } else {
                //去主页面
                MainPageRouter.jumpToMainActivity(context);
            }
        }
    }

}
