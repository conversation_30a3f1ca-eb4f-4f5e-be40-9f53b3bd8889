package com.kit.activity.login.util

import android.content.Context
import android.content.SharedPreferences
import com.kanzhun.common.base.BaseApplication
import com.techwolf.lib.tlog.TLog

/**
 * 自动登录开关管理工具类
 * 用于管理自动登录功能的开关状态
 */
object AutoLoginSwitchHelper {
    
    private const val TAG = "AutoLoginSwitchHelper"
    private const val PREF_NAME = "auto_login_switch"
    private const val KEY_AUTO_LOGIN_ENABLED = "auto_login_enabled"
    
    private val sharedPreferences: SharedPreferences by lazy {
        BaseApplication.getApplication().getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 设置自动登录开关状态
     * 
     * @param enabled true 开启自动登录，false 关闭自动登录
     */
    fun setAutoLoginEnabled(enabled: Boolean) {
        TLog.info(TAG, "设置自动登录开关状态: $enabled")
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_LOGIN_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 获取自动登录开关状态
     * 包含debug环境检查，只有debug环境下才可能返回true
     *
     * @return true 如果debug环境且自动登录开启，false 否则
     */
    fun isAutoLoginEnabled(): Boolean {
        // 首先检查是否为debug环境
        if (!com.kanzhun.marry.BuildConfig.DEBUG) {
            TLog.info(TAG, "非debug环境，自动登录功能不可用")
            return false
        }

        // debug环境下检查开关状态，默认开启
        val enabled = sharedPreferences.getBoolean(KEY_AUTO_LOGIN_ENABLED, true)
        TLog.info(TAG, "debug环境，获取自动登录开关状态: $enabled")
        return enabled
    }
    
    /**
     * 检查是否可以使用自动登录
     * 需要同时满足：1. 开关开启 2. 环境支持
     * 
     * @return true 如果可以使用自动登录，false 否则
     */
    fun canUseAutoLogin(): Boolean {
        val switchEnabled = isAutoLoginEnabled()
        val environmentSupported = AutoLoginEnvironmentHelper.isSupportAutoLogin()
        
        val canUse = switchEnabled && environmentSupported
        TLog.info(TAG, "检查是否可以使用自动登录: switchEnabled=$switchEnabled, environmentSupported=$environmentSupported, canUse=$canUse")
        
        return canUse
    }
}
