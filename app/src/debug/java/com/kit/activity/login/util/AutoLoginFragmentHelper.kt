package com.kit.activity.login.util

import com.kanzhun.marry.kotlin.router.LoginResultRouter
import com.kanzhun.marry.login.viewmodel.LoginViewModel
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog

/**
 * 自动登录Fragment辅助工具类
 * 用于在LoginInputNumberFragment中处理自动登录逻辑
 * 最小化对Fragment的修改
 */
object AutoLoginFragmentHelper {
    
    private const val TAG = "AutoLoginFragmentHelper"
    
    /**
     * 尝试自动登录
     * 前置条件已在Fragment中检查（debug环境 + 开关开启），这里只需检查环境支持和执行登录
     *
     * @param viewModel LoginViewModel实例
     */
    @JvmStatic
    fun tryAutoLogin(viewModel: LoginViewModel) {
        TLog.info(TAG, "=== 开始自动登录执行 ===")
        TLog.info(TAG, "前置条件已满足：debug环境 + 开关已开启")

        // 1. 检查当前环境是否支持自动登录
        if (!AutoLoginEnvironmentHelper.isSupportAutoLogin()) {
            TLog.info(TAG, "当前环境不支持自动登录，走原有流程")
            TLog.info(TAG, "当前环境: ${AutoLoginEnvironmentHelper.getCurrentEnvironmentName()}")
            viewModel.toCodeFragment()
            return
        }

        // 2. 检查手机号是否有效
        val phoneNumber = viewModel.phoneNumberObservable.get()?.replace(" ", "") ?: ""
        TLog.info(TAG, "手机号: $phoneNumber")

        if (phoneNumber.isBlank()) {
            TLog.info(TAG, "手机号为空，走原有流程")
            viewModel.toCodeFragment()
            return
        }

        // 3. 所有条件都满足，开始自动登录
        TLog.info(TAG, "=== 满足所有自动登录条件，开始执行自动登录 ===")
        TLog.info(TAG, "当前环境: ${AutoLoginEnvironmentHelper.getCurrentEnvironmentName()}")
        TLog.info(TAG, "开关状态: 已开启")
        TLog.info(TAG, "手机号: $phoneNumber")

        performAutoLogin(viewModel, phoneNumber)
    }
    
    /**
     * 执行自动登录
     * 
     * @param viewModel LoginViewModel实例
     * @param phoneNumber 手机号
     */
    private fun performAutoLogin(viewModel: LoginViewModel, phoneNumber: String) {
        TLog.info(TAG, "=== 开始执行自动登录 ===")
        TLog.info(TAG, "手机号: $phoneNumber")
        
        AutoLoginHelper.performAutoLogin(
            phone = phoneNumber,
            onSuccess = { loginModel ->
                TLog.info(TAG, "=== 自动登录成功 ===")
                TLog.info(TAG, "登录结果: userId=${loginModel.userId ?: "null"}, nickName=${loginModel.nickName ?: "null"}")
                
                try {
                    // 复用现有的登录成功处理逻辑
                    TLog.info(TAG, "调用 LoginResultRouter.routerAfterLoginSuccess...")
                    TLog.info(TAG, "loginModel 详细信息: userId=${loginModel.userId}, accountType=${loginModel.accountType}, phase=${loginModel.phase}")
                    LoginResultRouter.routerAfterLoginSuccess(loginModel)
                    TLog.info(TAG, "LoginResultRouter.routerAfterLoginSuccess 调用完成")
                    T.ss("自动登录成功")
                } catch (e: Exception) {
                    TLog.error(TAG, "处理自动登录成功时发生异常: ${e.message}", e)
                    T.ss("登录成功但处理过程中出现异常")
                }
            },
            onFail = { errorReason ->
                TLog.error(TAG, "=== 自动登录失败 ===")
                TLog.error(TAG, "错误码: ${errorReason.errCode ?: "null"}")
                TLog.error(TAG, "错误信息: ${errorReason.errReason ?: "null"}")
                
                // 自动登录失败，回退到原有流程
                T.ss("自动登录失败，使用验证码登录")
                viewModel.toCodeFragment()
            }
        )
    }
}
