package com.kit.activity.login.util

import com.kanzhun.foundation.bean.SMSLoginModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.login.api.LoginApi
import com.techwolf.lib.tlog.TLog

/**
 * 自动登录工具类
 * 用于实现真正的自动登录功能
 */
object AutoLoginHelper {
    
    /**
     * 执行自动登录
     * @param phone 手机号
     * @param onSuccess 登录成功回调
     * @param onFail 登录失败回调
     */
    fun performAutoLogin(
        phone: String,
        onSuccess: (SMSLoginModel) -> Unit = {},
        onFail: (ErrorReason) -> Unit = {}
    ) {
        TLog.info("AutoLoginHelper", "=== 开始自动登录流程 ===")
        TLog.info("AutoLoginHelper", "手机号: $phone")
        TLog.info("AutoLoginHelper", "创建 LoginApi 实例...")

        val loginApi = RetrofitManager.getInstance().createApi(LoginApi::class.java)
        TLog.info("AutoLoginHelper", "LoginApi 创建成功，调用 autoLogin 接口...")
        TLog.info("AutoLoginHelper", "接口 URL: orange/miniapp/register/test/login")
        TLog.info("AutoLoginHelper", "请求参数: phone=$phone")

        val baseResponseObservable = loginApi.autoLogin(phone)
        TLog.info("AutoLoginHelper", "开始执行网络请求...")

        HttpExecutor.execute<SMSLoginModel>(
            baseResponseObservable,
            object : BaseRequestCallback<SMSLoginModel>(true) {

                override fun onStart(disposable: io.reactivex.rxjava3.disposables.Disposable) {
                    super.onStart(disposable)
                    TLog.info("AutoLoginHelper", "网络请求开始...")
                }

                override fun onComplete() {
                    super.onComplete()
                    TLog.info("AutoLoginHelper", "网络请求完成")
                }
                override fun onSuccess(data: SMSLoginModel) {
                    TLog.info("AutoLoginHelper", "=== 自动登录接口调用成功 ===")
                    TLog.info("AutoLoginHelper", "返回数据: userId=${data.userId ?: "null"}, nickName=${data.nickName ?: "null"}")
                    TLog.info("AutoLoginHelper", "开始执行登录成功后续处理...")

                    try {
                        // 执行成功回调，让调用方处理后续逻辑（包括页面跳转）
                        onSuccess(data)
                        TLog.info("AutoLoginHelper", "成功回调执行完成")

                        TLog.info("AutoLoginHelper", "=== 自动登录流程完成 ===")
                    } catch (e: Exception) {
                        TLog.error("AutoLoginHelper", "登录成功后续处理异常: ${e.message}", e)
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                    TLog.error("AutoLoginHelper", "=== 自动登录接口调用失败 ===")
                    TLog.error("AutoLoginHelper", "错误码: ${reason.errCode ?: "null"}")
                    TLog.error("AutoLoginHelper", "错误信息: ${reason.errReason ?: "null"}")

                    onFail(reason)
                    TLog.info("AutoLoginHelper", "=== 自动登录失败，结束流程 ===")
                }
            }
        )
    }
}
