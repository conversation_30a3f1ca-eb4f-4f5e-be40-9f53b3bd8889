package com.kanzhun.marry.kotlin.router

import android.app.Activity
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.bean.SMSLoginModel
import com.kanzhun.foundation.kernel.account.Account
import com.kanzhun.foundation.kernel.account.AccountType
import com.kanzhun.foundation.kernel.account.LoginUtil
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.marry.push.PushSdkManager

class LoginResultRouter {
    companion object {
        /**
         * 处理登录成功后的跳转
         */
        @JvmStatic
        fun routerAfterLoginSuccess(resp: SMSLoginModel?) {
            val topContext = BaseApplication.getApplication().topContext
            if (topContext !is Activity) {
                return
            }
            val contexts = BaseApplication.getApplication().allActivity.map { it }

            resp?.run {
                if (wechatCode.isNullOrBlank()) {
                    LoginUtil.loginInit(this)
                    if (accountType == AccountType.NONE.value) {
                        //去选择模式
                        LoginPageRouter.jumpToSelectModel(topContext, true)
                        closeBeforeActivity(contexts)
                    } else if (accountType == AccountType.CHILD.value && phase == Account.ACCOUNT_STATUS_REGISTER) {
                        //去激活
//                        LoginPageRouter.jumpToSelectModel(topContext, true)
                        LoginPageRouter.jumpToInviteCode(topContext, true)
                        closeBeforeActivity(contexts)
                    } else {
                        //去首善或者主界面
                        val contexts = BaseApplication.getApplication().allActivity.map { it }
                        LoginUtil.goFirstEditOrMain(topContext)
                        closeBeforeActivity(contexts)
                    }
                    PushSdkManager.getInstance().start()
                } else {
                    //如果微信登录的code不为空，则去手机号注册登录页
                    LoginPageRouter.jumpToPhoneLoginActivity(BaseApplication.getApplication().topContext, wechatCode, false)
                }
            }
        }

        fun closeBeforeActivity(contexts:List<Activity>){
            contexts.forEach {
                AppUtil.finishActivityDelay(it)
            }
        }
    }
}