package com.kanzhun.marry.login.fragment.login;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.databinding.Observable;
import androidx.lifecycle.Observer;
import androidx.navigation.Navigation;

import com.kanzhun.common.util.LText;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.BR;
import com.kanzhun.marry.BuildConfig;
import com.kanzhun.marry.R;
import com.kanzhun.marry.databinding.FragmentLoginInputNumberBinding;
import com.kanzhun.marry.login.point.LoginPointAction;
import com.kanzhun.marry.login.viewmodel.LoginInputNumberViewModel;
import com.kanzhun.marry.login.viewmodel.LoginViewModel;
import com.kanzhun.marry.me.views.LoginNextPageView;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;


/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/10
 * 输入手机号页面
 */
public class LoginInputNumberFragment extends FoundationVMShareFragment<FragmentLoginInputNumberBinding, LoginInputNumberViewModel, LoginViewModel> {

    public static final String KEY_QA_PHONE = "key_qa_phone";

    @Override
    public int getContentLayoutId() {
        return R.layout.fragment_login_input_number;
    }

    @Override
    protected void initFragment() {
        getActivityViewModel().wechatCodeData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                getDataBinding().tvPhoneTitle.setTitle(TextUtils.isEmpty(s) ? getResources().getString(R.string.login_you_phone_number)
                        : "绑定关联手机号");
            }
        });
        getDataBinding().editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getActivityViewModel().phoneErrorObservable.set(false);
                getActivityViewModel().phoneErrorLiveData.setValue(false);
                getActivityViewModel().phoneErrorDescObservable.set("");
            }

            @Override
            public void afterTextChanged(Editable s) {
                setBtnNextStatue(s.toString());
            }
        });
        getDataBinding().editText.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("手机号输入框");
                        pointBean.setActionp2(getActivityViewModel().getSource());
                        return null;
                    }
                });
            }
        });
        getDataBinding().editText.performClick();
        QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, true);
        getDataBinding().btnNext.setState(LoginNextPageView.STATE.IDLE);
        getDataBinding().btnNext.setAnimClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                getDataBinding().btnNext.setState(LoginNextPageView.STATE.LOADING);

                // 尝试自动登录（所有判断逻辑都在工具类中）
                if (BuildConfig.DEBUG) {
                    tryAutoLoginWithHelper();
                } else {
                    getActivityViewModel().toCodeFragment();
                }

                PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("下一步");
                        pointBean.setActionp2(getActivityViewModel().getSource());
                        return null;
                    }
                });
            }
        });

        getActivityViewModel().getInputSuccessLivaData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    getActivityViewModel().getInputSuccessLivaData().setValue(false);
                    Navigation.findNavController(getDataBinding().btnNext).navigate(R.id.action_inputNumberFragment_to_inputCodeFragment);
                }
            }
        });


        getDataBinding().ivNumberDelete.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                getActivityViewModel().phoneNumberObservable.set("");
                PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("清空输入框");
                        pointBean.setActionp2(getActivityViewModel().getSource());
                        return null;
                    }
                });
            }
        });
        getActivityViewModel().phoneNumberObservable.addOnPropertyChangedCallback(new Observable.OnPropertyChangedCallback() {
            @Override
            public void onPropertyChanged(Observable sender, int propertyId) {
                if (TextUtils.isEmpty(getActivityViewModel().phoneNumberObservable.get())) {
                    getDataBinding().ivNumberDelete.setVisibility(View.GONE);
                } else {
                    getDataBinding().ivNumberDelete.setVisibility(View.VISIBLE);
                }
            }
        });

        getActivityViewModel().geetestonClosedLiveData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer > 0) {
                    getDataBinding().btnNext.setState(LoginNextPageView.STATE.ERROR);
                }
            }
        });

        getActivityViewModel().phoneErrorLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (getActivityViewModel().phoneErrorLiveData.getValue()) {
                    getDataBinding().btnNext.setState(LoginNextPageView.STATE.ERROR);
                } else {
                    getDataBinding().btnNext.setState(LoginNextPageView.STATE.IDLE);
                }
            }
        });

        if (BuildConfig.DEBUG) {
            Bundle args = getArguments();
            if (args != null) {
                String phone = args.getString(KEY_QA_PHONE);
                if (phone != null) {
                    getDataBinding().editText.setText(phone);
                    getDataBinding().editText
                            .postDelayed(() -> getDataBinding().btnNext.performClick(), 400);
                }
            }
        }
    }


    @Override
    public void onResume() {
        super.onResume();
        PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setMsg(getActivityViewModel().phoneErrorDescObservable.get());
                pointBean.setActionp2(getActivityViewModel().getSource());
                return null;
            }
        });
    }


    private void setBtnNextStatue(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            getDataBinding().btnNext.setEnabled(false);
            return;
        }
        getDataBinding().btnNext.setEnabled(phoneNum.length() >= 11);
        if (phoneNum.length() >= 11) {
            if (!LText.isMobile("86", phoneNum)) {
                getActivityViewModel().phoneErrorDescObservable.set(getResources().getString(R.string.login_input_right_phone_number));
                getActivityViewModel().phoneErrorObservable.set(true);
                getActivityViewModel().phoneErrorLiveData.setValue(true);
                PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_EXPO, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setMsg(getActivityViewModel().phoneErrorDescObservable.get());
                        pointBean.setActionp2(getActivityViewModel().getSource());
                        return null;
                    }
                });
            }
        }
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    /**
     * 使用工具类尝试自动登录
     * 检查开关状态（包含debug环境检查），满足条件才使用反射调用debug版本的工具类
     */
    private void tryAutoLoginWithHelper() {
        // 1. 检查一键登录开关是否开启（包含debug环境检查）
        boolean autoLoginEnabled = false;
        try {
            Class<?> switchHelperClass = Class.forName("com.kit.activity.login.util.AutoLoginSwitchHelper");
            // Kotlin object 需要通过 INSTANCE 字段获取实例
            Object instance = switchHelperClass.getField("INSTANCE").get(null);
            java.lang.reflect.Method isEnabledMethod = switchHelperClass.getMethod("isAutoLoginEnabled");
            Boolean result = (Boolean) isEnabledMethod.invoke(instance);
            autoLoginEnabled = result != null && result;
        } catch (Exception e) {
            // 如果获取开关状态失败，默认为false
            // 添加日志记录异常信息
            com.techwolf.lib.tlog.TLog.error("LoginInputNumberFragment", "反射调用 AutoLoginSwitchHelper.isAutoLoginEnabled 失败: " + e.getMessage(), e);
        }

        if (!autoLoginEnabled) {
            // 非debug环境或开关未开启，走原有流程
            getActivityViewModel().toCodeFragment();
            return;
        }

        // 2. debug环境且开关开启，尝试调用自动登录工具类
        try {
            Class<?> helperClass = Class.forName("com.kit.activity.login.util.AutoLoginFragmentHelper");
            java.lang.reflect.Method tryAutoLoginMethod = helperClass.getMethod("tryAutoLogin", getActivityViewModel().getClass());
            tryAutoLoginMethod.invoke(null, getActivityViewModel());
        } catch (Exception e) {
            // 如果反射调用失败，走原有流程
            com.techwolf.lib.tlog.TLog.error("LoginInputNumberFragment", "反射调用 AutoLoginFragmentHelper.tryAutoLogin 失败: " + e.getMessage(), e);
            getActivityViewModel().toCodeFragment();
        }
    }

}
