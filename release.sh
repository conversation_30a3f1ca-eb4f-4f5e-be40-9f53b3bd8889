#source ~/.bash_profile

packageName="com.kanzhun.marry"
module="app"
configuration="prodReleaseRuntimeClasspath"
appDir=`pwd`

echo "---------commitID----------"
echo $commitID
echo "----------------------------"
git checkout $commitID

# get app version
VERSION_INFO=$(./gradlew properties | grep -E 'appShowVersionName:|appVersionCode:')
echo "---------VERSION_INFO----------"
echo "${VERSION_INFO}"
echo "---------VERSION_INFO----------"

VERSION_ARRAY=($VERSION_INFO)
echo "---------VERSION_ARRAY----------"
echo "${VERSION_ARRAY}"
echo "---------VERSION_ARRAY----------"

versionCode="2090010"
versionName="2.9.0"

echo ${#VERSION_ARRAY[@]}

for ((i=0;i<${#VERSION_ARRAY[@]};i++))
do
	if [ "appVersionCode:" = "${VERSION_ARRAY[i]}" ];then
		versionCode="${VERSION_ARRAY[++i]}"
	fi

	if [ "appShowVersionName:" = "${VERSION_ARRAY[i]}" ];then
		versionName="${VERSION_ARRAY[++i]}"
	fi
done;

echo '获取的参数如下：'
echo $versionCode
echo $versionName

# 获取App信息及全量SDK列表
base_path='/Users/<USER>/Documents/scripts/sdkAnalyzer/'
echo "---------------base_path------------"
echo $base_path
echo "---------------base_path------------"
save_path=$base_path/sdk_list.json
echo $save_path
rm -f $save_path

echo "---------------appDir------------"
echo $appDir
echo "---------------------------"
java -jar $base_path/sdkAnalyzer-1.0.0.jar --mode=3 --path=$appDir --module=$module --configuration=$configuration --savePath=$save_path --packageName=$packageName --versionCode=$versionCode --versionName=$versionName
if [ -f $save_path ] ; then
    # 发给静态检测平台
    echo "---------------apk_sdk_data------------"
    apk_sdk_data=`cat $save_path`
    echo $apk_sdk_data


    # 本地环境
    #api_base_path='http://************:8000'
    #authorization='c105015bf1eab3f0dffc2a38b56eefdc141d283ed9b27ccec3e785b69c2a70a2'

    # prod环境
    api_base_path='http://safety.android4.weizhipin.com'
    authorization='ea25e7ac9ccc2c339b4ceb026fabfa285a924f560bd85bf28d7b6ed80a644f45'

    curl \
	    --verbose \
    	--request POST \
     	--location $api_base_path'/api/v1/dependencies' \
     	--header 'Authorization: '$authorization \
     	--header 'Content-Type: application/json' \
     	--data $apk_sdk_data
else
    echo 'Failed to get app info and sdk list'
    exit 1
fi