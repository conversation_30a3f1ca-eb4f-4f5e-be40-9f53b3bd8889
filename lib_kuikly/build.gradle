plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.plugin.serialization'
}
apply from: "$rootDir/gradle/common_library.gradle"

android {
    namespace = "com.kanzhun.marry.lib_kuikly"
    defaultConfig {
//        resourcePrefix 'share_'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    testImplementation 'junit:junit:4.+'
    kapt deps.wmrouter_compiler
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation deps.androidx.app_compat
    implementation deps.androidx.recyclerview
    implementation project(":lib_foundation")

    implementation("com.tencent.kuikly-open:core-render-android:2.0.0-2.0.21")
    implementation("com.tencent.kuikly-open:core:2.0.0-2.0.21")

    implementation project(path: ':lib_imageviewer')
    
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0"
}